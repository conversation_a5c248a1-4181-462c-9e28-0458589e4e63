<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
    <!-- Header -->
    <header class="bg-black/20 backdrop-blur-lg border-b border-white/10">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center">
            <h1 class="text-xl font-bold text-white">Ghost Tweaker</h1>
            <span class="ml-3 px-2 py-1 bg-purple-600 text-white text-xs rounded-full">
              {{ authStore.user?.isPremium ? 'Premium' : 'Free' }}
            </span>
          </div>

          <div class="flex items-center space-x-4">
            <!-- System Status -->
            <div class="flex items-center space-x-2">
              <div :class="[
                'w-2 h-2 rounded-full',
                systemStore.isSystemReady ? 'bg-green-400' : 'bg-red-400'
              ]"></div>
              <span class="text-sm text-slate-300">
                {{ systemStore.isSystemReady ? 'System Ready' : 'System Issues' }}
              </span>
            </div>

            <!-- User Menu -->
            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <button class="flex items-center space-x-2 text-slate-300 hover:text-white">
                  <span class="text-sm">{{ authStore.user?.username }}</span>
                  <ChevronDown class="w-4 h-4" />
                </button>
              </DropdownMenuTrigger>

              <DropdownMenuContent
                align="end"
                :side-offset="8"
                class="w-48 bg-white/10 backdrop-blur-lg rounded-lg shadow-lg border border-white/20 z-50 p-1">
                <DropdownMenuItem
                  @click="$router.push('/debug')"
                  class="px-4 py-2 text-sm text-slate-300 hover:text-white hover:bg-white/10 cursor-pointer rounded-md">
                  🔧 Debug Panel
                </DropdownMenuItem>
                <DropdownMenuItem
                  @click="handleLogout"
                  class="px-4 py-2 text-sm text-slate-300 hover:text-white hover:bg-white/10 cursor-pointer rounded-md">
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- System Health Overview -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <SystemHealthCard
          label="CPU Usage"
          :value="`${cpuUsage}%`"
          :icon="BarChart3"
          color="blue"
        />
        <SystemHealthCard
          label="Memory Usage"
          :value="`${memoryUsage}%`"
          :icon="Database"
          color="green"
        />
        <SystemHealthCard
          label="Disk Usage"
          :value="`${diskUsage}%`"
          :icon="HardDrive"
          color="purple"
        />
        <SystemHealthCard
          label="Processes"
          :value="processCount"
          :icon="Layers"
          color="yellow"
        />
      </div>

      <!-- Quick Actions -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- System Tweaks -->
        <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
          <h2 class="text-xl font-bold text-white mb-6">System Tweaks</h2>

          <div class="space-y-4">
            <TweakToggle
              title="Network Optimization"
              description="Optimize TCP settings for gaming"
              :enabled="networkTweaksEnabled"
              :disabled="systemStore.isLoading"
              @toggle="toggleNetworkTweaks"
            />

            <TweakToggle
              title="FPS Boost"
              description="Stop unnecessary services and optimize power plan"
              :enabled="fpsBoostEnabled"
              :disabled="systemStore.isLoading"
              @toggle="toggleFpsBoost"
            />
            <TweakToggle
              title="Advanced Registry"
              description="Apply advanced system responsiveness tweaks"
              :enabled="advancedRegistryEnabled"
              :disabled="systemStore.isLoading"
              @toggle="toggleAdvancedRegistry"
            />

            <TweakToggle
              title="Timer Resolution"
              description="Reduce input lag with high-resolution timer (1ms)"
              :enabled="timerResolutionEnabled"
              :disabled="systemStore.isLoading"
              @toggle="toggleTimerResolution"
            />
                  timerResolutionEnabled ? 'translate-x-6' : 'translate-x-1'
                ]"></span>
              </button>
            </div>
          </div>

          <div class="mt-6 space-y-3">
            <button @click="optimizeRAM" :disabled="systemStore.isLoading"
              class="w-full py-3 px-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
              Optimize RAM
            </button>

            <button @click="createRestorePoint" :disabled="systemStore.isLoading"
              class="w-full py-3 px-4 bg-gradient-to-r from-blue-600 to-cyan-600 text-white font-medium rounded-lg hover:from-blue-700 hover:to-cyan-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
              Create Restore Point
            </button>
          </div>
        </div>

        <!-- System Information -->
        <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-bold text-white">System Information</h2>
            <button @click="refreshSystemHealth" :disabled="systemStore.isLoading" :class="[
              'p-2 transition-colors',
              systemStore.isLoading ? 'text-purple-400' : 'text-slate-400 hover:text-white'
            ]">
              <svg :class="[
                'w-5 h-5',
                systemStore.isLoading || systemStore.isBackgroundLoading ? 'animate-spin' : ''
              ]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                </path>
              </svg>
            </button>
          </div>

          <div class="space-y-4">
            <StatusIndicator
              label="Backend Service"
              :is-connected="isBackendConnected"
              connected-text="Running"
              disconnected-text="Stopped"
            />

            <StatusIndicator
              label="Auth Server"
              :is-connected="isAuthServerConnected"
              connected-text="Connected"
              disconnected-text="Disconnected"
            />

            <div v-if="systemStore.systemHealth" class="p-4 bg-black/20 rounded-lg">
              <div class="text-slate-300 text-sm">
                <p>Uptime: {{ systemUptime }}</p>
                <p>Last Updated: {{ lastUpdated }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useSystemStore } from '@/stores/system'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { ChevronDown, BarChart3, Database, HardDrive, Layers } from 'lucide-vue-next'
import SystemHealthCard from '@/components/SystemHealthCard.vue'
import TweakToggle from '@/components/TweakToggle.vue'
import StatusIndicator from '@/components/StatusIndicator.vue'

const router = useRouter()
const authStore = useAuthStore()
const systemStore = useSystemStore()

// Simplified computed properties using system store
const networkTweaksEnabled = computed(() => systemStore.applicationState?.tweakStates?.networkTweaksEnabled ?? false)
const fpsBoostEnabled = computed(() => systemStore.applicationState?.tweakStates?.fpsBoostEnabled ?? false)
const advancedRegistryEnabled = computed(() => systemStore.applicationState?.tweakStates?.advancedRegistryEnabled ?? false)
const timerResolutionEnabled = computed(() => systemStore.applicationState?.timerResolution?.isEnabled ?? false)

// Service status from system store
const isBackendConnected = computed(() => systemStore.serviceStatus?.backendServiceRunning ?? false)
const isAuthServerConnected = computed(() => systemStore.serviceStatus?.authServerConnected ?? false)

let healthInterval: number | null = null

const handleLogout = async () => {
  try {
    await authStore.logout()
    // Navigation will be handled by the router guard after logout
    router.push('/login')
  } catch (error) {
    console.error('Logout failed:', error)
    // Still redirect to login even if logout request failed
    router.push('/login')
  }
}



const toggleNetworkTweaks = async () => {
  const newState = !networkTweaksEnabled.value
  const result = await systemStore.applyNetworkTweaks(newState)
  if (result.success) {
    await systemStore.refreshSystemState()
  }
}

const toggleFpsBoost = async () => {
  const newState = !fpsBoostEnabled.value
  const result = await systemStore.applyFpsBoostTweaks(newState)
  if (result.success) {
    await systemStore.refreshSystemState()
  }
}

const toggleAdvancedRegistry = async () => {
  const newState = !advancedRegistryEnabled.value
  const result = await systemStore.applyAdvancedRegistryTweaks(newState)
  if (result.success) {
    await systemStore.refreshSystemState()
  }
}

const toggleTimerResolution = async () => {
  const newState = !timerResolutionEnabled.value
  const result = await systemStore.applyTimerResolution(newState)
  if (result.success) {
    await systemStore.refreshSystemState()
  }
}

const optimizeRAM = async () => {
  await systemStore.applyRamOptimization()
}

const createRestorePoint = async () => {
  const description = `Ghost Tweaker - ${new Date().toLocaleString()}`
  await systemStore.createSystemRestorePoint(description)
}

const refreshSystemHealth = async () => {
  await systemStore.refreshSystemState()
}

// Simplified computed properties using system store
const cpuUsage = computed(() => systemStore.systemHealth?.cpuUsage?.toFixed(1) || '--')
const memoryUsage = computed(() => systemStore.systemHealth?.memoryUsage?.toFixed(1) || '--')
const diskUsage = computed(() => systemStore.systemHealth?.diskUsage?.toFixed(1) || '--')
const processCount = computed(() => systemStore.systemHealth?.processCount || '--')
const systemUptime = computed(() => systemStore.systemHealth?.uptime || '--')
const lastUpdated = computed(() =>
  systemStore.systemHealth?.timestamp
    ? new Date(systemStore.systemHealth.timestamp).toLocaleTimeString()
    : '--'
)

onMounted(async () => {
  // Initialize system and load initial state
  await systemStore.initializeSystem()
  await systemStore.refreshSystemState()

  // Set up periodic state updates (simplified)
  healthInterval = window.setInterval(async () => {
    await systemStore.refreshSystemState()
  }, 5000) // Update every 5 seconds
})

onUnmounted(() => {
  if (healthInterval) {
    clearInterval(healthInterval)
  }
})
</script>
