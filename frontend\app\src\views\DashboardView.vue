<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
    <!-- Header -->
    <header class="bg-black/20 backdrop-blur-lg border-b border-white/10">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center">
            <h1 class="text-xl font-bold text-white">Ghost Tweaker</h1>
            <span class="ml-3 px-2 py-1 bg-purple-600 text-white text-xs rounded-full">
              {{ authStore.user?.isPremium ? 'Premium' : 'Free' }}
            </span>
          </div>

          <div class="flex items-center space-x-4">
            <!-- System Status -->
            <div class="flex items-center space-x-2">
              <div :class="[
                'w-2 h-2 rounded-full',
                systemStore.isSystemReady ? 'bg-green-400' : 'bg-red-400'
              ]"></div>
              <span class="text-sm text-slate-300">
                {{ systemStore.isSystemReady ? 'System Ready' : 'System Issues' }}
              </span>
            </div>

            <!-- User Menu -->
            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <button class="flex items-center space-x-2 text-slate-300 hover:text-white">
                  <span class="text-sm">{{ authStore.user?.username }}</span>
                  <ChevronDown class="w-4 h-4" />
                </button>
              </DropdownMenuTrigger>

              <DropdownMenuContent
                align="end"
                :side-offset="8"
                class="w-48 bg-white/10 backdrop-blur-lg rounded-lg shadow-lg border border-white/20 z-50 p-1">
                <DropdownMenuItem
                  @click="$router.push('/debug')"
                  class="px-4 py-2 text-sm text-slate-300 hover:text-white hover:bg-white/10 cursor-pointer rounded-md">
                  🔧 Debug Panel
                </DropdownMenuItem>
                <DropdownMenuItem
                  @click="handleLogout"
                  class="px-4 py-2 text-sm text-slate-300 hover:text-white hover:bg-white/10 cursor-pointer rounded-md">
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- System Health Overview -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
          <div class="flex items-center">
            <div class="p-2 bg-blue-500/20 rounded-lg">
              <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                </path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-slate-300">CPU Usage</p>
              <p class="text-2xl font-bold text-white">
                {{ cpuUsage }}%
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
          <div class="flex items-center">
            <div class="p-2 bg-green-500/20 rounded-lg">
              <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4">
                </path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-slate-300">Memory Usage</p>
              <p class="text-2xl font-bold text-white">
                {{ memoryUsage }}%
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
          <div class="flex items-center">
            <div class="p-2 bg-purple-500/20 rounded-lg">
              <svg class="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z">
                </path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-slate-300">Disk Usage</p>
              <p class="text-2xl font-bold text-white">
                {{ diskUsage }}%
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
          <div class="flex items-center">
            <div class="p-2 bg-yellow-500/20 rounded-lg">
              <svg class="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z">
                </path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-slate-300">Processes</p>
              <p class="text-2xl font-bold text-white">
                {{ processCount }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- System Tweaks -->
        <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
          <h2 class="text-xl font-bold text-white mb-6">System Tweaks</h2>

          <div class="space-y-4">


            <div class="flex items-center justify-between p-4 bg-black/20 rounded-lg">
              <div>
                <h3 class="font-medium text-white">Network Optimization</h3>
                <p class="text-sm text-slate-400">Optimize TCP settings for gaming</p>
              </div>
              <button @click="toggleNetworkTweaks" :disabled="systemStore.isLoading" :class="[
                'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
                networkTweaksEnabled ? 'bg-purple-600' : 'bg-gray-600'
              ]">
                <span :class="[
                  'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
                  networkTweaksEnabled ? 'translate-x-6' : 'translate-x-1'
                ]"></span>
              </button>
            </div>

            <div class="flex items-center justify-between p-4 bg-black/20 rounded-lg">
              <div>
                <h3 class="font-medium text-white">FPS Boost</h3>
                <p class="text-sm text-slate-400">Stop unnecessary services and optimize power plan</p>
              </div>
              <button @click="toggleFpsBoost" :disabled="systemStore.isLoading" :class="[
                'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
                fpsBoostEnabled ? 'bg-purple-600' : 'bg-gray-600'
              ]">
                <span :class="[
                  'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
                  fpsBoostEnabled ? 'translate-x-6' : 'translate-x-1'
                ]"></span>
              </button>
            </div>

            <div class="flex items-center justify-between p-4 bg-black/20 rounded-lg">
              <div>
                <h3 class="font-medium text-white">Advanced Registry</h3>
                <p class="text-sm text-slate-400">Apply advanced system responsiveness tweaks</p>
              </div>
              <button @click="toggleAdvancedRegistry" :disabled="systemStore.isLoading" :class="[
                'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
                advancedRegistryEnabled ? 'bg-purple-600' : 'bg-gray-600'
              ]">
                <span :class="[
                  'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
                  advancedRegistryEnabled ? 'translate-x-6' : 'translate-x-1'
                ]"></span>
              </button>
            </div>

            <div class="flex items-center justify-between p-4 bg-black/20 rounded-lg">
              <div>
                <h3 class="font-medium text-white">Timer Resolution</h3>
                <p class="text-sm text-slate-400">Reduce input lag with high-resolution timer (1ms)</p>
              </div>
              <button @click="toggleTimerResolution" :disabled="systemStore.isLoading" :class="[
                'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
                timerResolutionEnabled ? 'bg-purple-600' : 'bg-gray-600'
              ]">
                <span :class="[
                  'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
                  timerResolutionEnabled ? 'translate-x-6' : 'translate-x-1'
                ]"></span>
              </button>
            </div>
          </div>

          <div class="mt-6 space-y-3">
            <button @click="optimizeRAM" :disabled="systemStore.isLoading"
              class="w-full py-3 px-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
              Optimize RAM
            </button>

            <button @click="createRestorePoint" :disabled="systemStore.isLoading"
              class="w-full py-3 px-4 bg-gradient-to-r from-blue-600 to-cyan-600 text-white font-medium rounded-lg hover:from-blue-700 hover:to-cyan-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
              Create Restore Point
            </button>
          </div>
        </div>

        <!-- System Information -->
        <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-bold text-white">System Information</h2>
            <button @click="refreshSystemHealth" :disabled="systemStore.isLoading" :class="[
              'p-2 transition-colors',
              systemStore.isLoading ? 'text-purple-400' : 'text-slate-400 hover:text-white'
            ]">
              <svg :class="[
                'w-5 h-5',
                systemStore.isLoading || systemStore.isBackgroundLoading ? 'animate-spin' : ''
              ]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                </path>
              </svg>
            </button>
          </div>

          <div class="space-y-4">
            <div class="p-4 bg-black/20 rounded-lg">
              <div class="flex justify-between items-center">
                <span class="text-slate-300">Backend Service</span>
                <div class="flex items-center space-x-2">
                  <div :class="[
                    'w-2 h-2 rounded-full',
                    isBackendConnected ? 'bg-green-400' : 'bg-red-400'
                  ]"></div>
                  <span :class="[
                    'text-sm',
                    isBackendConnected ? 'text-green-400' : 'text-red-400'
                  ]">
                    {{ isBackendConnected ? 'Connected' : 'Disconnected' }}
                  </span>
                  <button v-if="!isBackendConnected" @click="systemStore.restartBackendService"
                    class="text-xs text-purple-400 hover:text-purple-300">
                    Restart
                  </button>
                </div>
              </div>
            </div>

            <div class="p-4 bg-black/20 rounded-lg">
              <div class="flex justify-between items-center">
                <span class="text-slate-300">Auth Server</span>
                <div class="flex items-center space-x-2">
                  <div :class="[
                    'w-2 h-2 rounded-full',
                    isAuthServerConnected ? 'bg-green-400' : 'bg-red-400'
                  ]"></div>
                  <span :class="[
                    'text-sm',
                    isAuthServerConnected ? 'text-green-400' : 'text-red-400'
                  ]">
                    {{ isAuthServerConnected ? 'Connected' : 'Disconnected' }}
                  </span>
                </div>
              </div>
            </div>

            <div v-if="systemStore.systemHealth" class="p-4 bg-black/20 rounded-lg">
              <div class="text-slate-300 text-sm">
                <p>Uptime: {{ systemUptime }}</p>
                <p>Last Updated: {{ lastUpdated }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useSystemStore } from '@/stores/system'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { ChevronDown } from 'lucide-vue-next'

const router = useRouter()
const authStore = useAuthStore()
const systemStore = useSystemStore()

// Simplified computed properties using system store
const networkTweaksEnabled = computed(() => systemStore.applicationState?.tweakStates?.networkTweaksEnabled ?? false)
const fpsBoostEnabled = computed(() => systemStore.applicationState?.tweakStates?.fpsBoostEnabled ?? false)
const advancedRegistryEnabled = computed(() => systemStore.applicationState?.tweakStates?.advancedRegistryEnabled ?? false)
const timerResolutionEnabled = computed(() => systemStore.applicationState?.timerResolution?.isEnabled ?? false)

// Service status from system store
const isBackendConnected = computed(() => systemStore.serviceStatus?.backendServiceRunning ?? false)
const isAuthServerConnected = computed(() => systemStore.serviceStatus?.authServerConnected ?? false)

let healthInterval: number | null = null

const handleLogout = async () => {
  try {
    await authStore.logout()
    // Navigation will be handled by the router guard after logout
    router.push('/login')
  } catch (error) {
    console.error('Logout failed:', error)
    // Still redirect to login even if logout request failed
    router.push('/login')
  }
}



const toggleNetworkTweaks = async () => {
  const newState = !networkTweaksEnabled.value
  const result = await systemStore.applyNetworkTweaks(newState)
  if (result.success) {
    await systemStore.refreshSystemState()
  }
}

const toggleFpsBoost = async () => {
  const newState = !fpsBoostEnabled.value
  const result = await systemStore.applyFpsBoostTweaks(newState)
  if (result.success) {
    await systemStore.refreshSystemState()
  }
}

const toggleAdvancedRegistry = async () => {
  const newState = !advancedRegistryEnabled.value
  const result = await systemStore.applyAdvancedRegistryTweaks(newState)
  if (result.success) {
    await systemStore.refreshSystemState()
  }
}

const toggleTimerResolution = async () => {
  const newState = !timerResolutionEnabled.value
  const result = await systemStore.applyTimerResolution(newState)
  if (result.success) {
    await systemStore.refreshSystemState()
  }
}

const optimizeRAM = async () => {
  await systemStore.applyRamOptimization()
}

const createRestorePoint = async () => {
  const description = `Ghost Tweaker - ${new Date().toLocaleString()}`
  await systemStore.createSystemRestorePoint(description)
}

const refreshSystemHealth = async () => {
  await systemStore.refreshSystemState()
}

// Simplified computed properties using system store
const cpuUsage = computed(() => systemStore.systemHealth?.cpuUsage?.toFixed(1) || '--')
const memoryUsage = computed(() => systemStore.systemHealth?.memoryUsage?.toFixed(1) || '--')
const diskUsage = computed(() => systemStore.systemHealth?.diskUsage?.toFixed(1) || '--')
const processCount = computed(() => systemStore.systemHealth?.processCount || '--')
const systemUptime = computed(() => systemStore.systemHealth?.uptime || '--')
const lastUpdated = computed(() =>
  systemStore.systemHealth?.timestamp
    ? new Date(systemStore.systemHealth.timestamp).toLocaleTimeString()
    : '--'
)

onMounted(async () => {
  // Initialize system and load initial state
  await systemStore.initializeSystem()
  await systemStore.refreshSystemState()

  // Set up periodic state updates (simplified)
  healthInterval = window.setInterval(async () => {
    await systemStore.refreshSystemState()
  }, 5000) // Update every 5 seconds
})

onUnmounted(() => {
  if (healthInterval) {
    clearInterval(healthInterval)
  }
})
</script>
