import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface ServiceError {
  type: 'backend' | 'auth' | 'both' | 'unknown'
  errorType?: string
  message: string
  details?: string
  servicePath?: string
  timestamp: Date
}

export interface ServiceStatus {
  backend: boolean
  auth: boolean
  lastChecked: Date | null
}

export const useServiceErrorStore = defineStore('serviceError', () => {
  // UI-only state
  const hasError = ref(false)
  const currentError = ref<ServiceError | null>(null)
  const isRetrying = ref(false)

  // Computed
  const errorTitle = computed(() => {
    if (!currentError.value) return 'Service Error'
    
    switch (currentError.value.type) {
      case 'backend':
        return 'Backend Service Required'
      case 'auth':
        return 'Authentication Server Unavailable'
      case 'both':
        return 'Multiple Service Failures'
      default:
        return 'Service Connection Error'
    }
  })

  const troubleshootingSteps = computed(() => {
    if (!currentError.value) return []
    
    const baseSteps = [
      'Check that the Ghost Tweaker backend service is running',
      'Verify network connectivity',
      'Try restarting the application'
    ]

    switch (currentError.value.type) {
      case 'backend':
        return [
          'Restart the Ghost Tweaker backend service',
          'Check if the service port is available',
          'Run the application as Administrator',
          ...baseSteps
        ]
      case 'auth':
        return [
          'Check internet connectivity',
          'Verify the authentication server is accessible',
          'Try logging in again',
          ...baseSteps
        ]
      case 'both':
        return [
          'Restart all services',
          'Check network and internet connectivity',
          'Run the application as Administrator',
          'Contact support if the issue persists'
        ]
      default:
        return baseSteps
    }
  })

  // Get current service status from backend
  const getServiceStatus = async (): Promise<ServiceStatus> => {
    try {
      if (!window.electronAPI) {
        return { backend: false, auth: false, lastChecked: new Date() }
      }

      const [backendStatus, authStatus] = await Promise.all([
        window.electronAPI.checkBackendStatus(),
        window.electronAPI.checkAuthServerStatus()
      ])

      return {
        backend: backendStatus,
        auth: authStatus,
        lastChecked: new Date()
      }
    } catch (err) {
      return { backend: false, auth: false, lastChecked: new Date() }
    }
  }

  // Check if services are available
  const checkServiceAvailability = async (): Promise<boolean> => {
    const status = await getServiceStatus()
    return status.backend && status.auth
  }

  // Set error state
  const setError = (error: ServiceError) => {
    hasError.value = true
    currentError.value = error
  }

  // Clear error state
  const clearError = () => {
    hasError.value = false
    currentError.value = null
  }

  // Retry service connection
  const retryConnection = async (): Promise<boolean> => {
    try {
      isRetrying.value = true
      
      if (window.electronAPI?.retryServiceConnection) {
        const result = await window.electronAPI.retryServiceConnection()
        
        if (result.success) {
          clearError()
          return true
        } else {
          setError({
            type: 'backend',
            message: 'Failed to restart services',
            details: result.error?.message || 'Unknown error',
            timestamp: new Date()
          })
          return false
        }
      }
      
      return false
    } catch (err: any) {
      setError({
        type: 'unknown',
        message: 'Error during service retry',
        details: err.message,
        timestamp: new Date()
      })
      return false
    } finally {
      isRetrying.value = false
    }
  }

  // Validate services and set error if needed
  const validateServices = async () => {
    try {
      const status = await getServiceStatus()
      
      if (!status.backend && !status.auth) {
        setError({
          type: 'both',
          message: 'Both backend and authentication services are unavailable',
          details: 'The application cannot function without these services',
          timestamp: new Date()
        })
      } else if (!status.backend) {
        setError({
          type: 'backend',
          message: 'Backend service is not running',
          details: 'The local backend service is required for all functionality',
          timestamp: new Date()
        })
      } else if (!status.auth) {
        setError({
          type: 'auth',
          message: 'Authentication server is unavailable',
          details: 'Cannot verify user credentials or access premium features',
          timestamp: new Date()
        })
      } else {
        clearError()
      }
    } catch (err: any) {
      setError({
        type: 'unknown',
        message: 'Error validating services',
        details: err.message,
        timestamp: new Date()
      })
    }
  }

  return {
    // State
    hasError,
    currentError,
    isRetrying,

    // Computed
    errorTitle,
    troubleshootingSteps,

    // Actions
    getServiceStatus,
    checkServiceAvailability,
    setError,
    clearError,
    retryConnection,
    validateServices
  }
})
