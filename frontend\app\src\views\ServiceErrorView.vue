<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
    <div class="w-full max-w-2xl">
      <!-- Error Icon and Title -->
      <div class="text-center mb-8">
        <div class="mx-auto w-20 h-20 bg-red-500/20 rounded-full flex items-center justify-center mb-4">
          <svg class="w-10 h-10 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
        </div>
        <h1 class="text-3xl font-bold text-white mb-2">Service Unavailable</h1>
        <p class="text-slate-300">Ghost Tweaker requires all services to be running properly</p>
      </div>

      <!-- Error Details Card -->
      <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 mb-6">
        <div class="flex items-start space-x-4">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </div>
          </div>
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-white mb-2">{{ errorTitle }}</h3>
            <p class="text-slate-300 mb-4">{{ errorMessage }}</p>
            
            <!-- Service Status List -->
            <div class="space-y-3">
              <div class="flex items-center justify-between p-3 bg-black/20 rounded-lg">
                <div class="flex items-center space-x-3">
                  <div :class="[
                    'w-3 h-3 rounded-full',
                    serviceStatus.backend ? 'bg-green-400' : 'bg-red-400'
                  ]"></div>
                  <span class="text-white font-medium">Local Backend Service</span>
                </div>
                <span :class="[
                  'text-sm px-2 py-1 rounded-full',
                  serviceStatus.backend ? 'bg-green-500/20 text-green-300' : 'bg-red-500/20 text-red-300'
                ]">
                  {{ serviceStatus.backend ? 'Running' : 'Stopped' }}
                </span>
              </div>

              <div class="flex items-center justify-between p-3 bg-black/20 rounded-lg">
                <div class="flex items-center space-x-3">
                  <div :class="[
                    'w-3 h-3 rounded-full',
                    serviceStatus.auth ? 'bg-green-400' : 'bg-red-400'
                  ]"></div>
                  <span class="text-white font-medium">Authentication Server</span>
                </div>
                <span :class="[
                  'text-sm px-2 py-1 rounded-full',
                  serviceStatus.auth ? 'bg-green-500/20 text-green-300' : 'bg-red-500/20 text-red-300'
                ]">
                  {{ serviceStatus.auth ? 'Connected' : 'Disconnected' }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Troubleshooting Steps -->
      <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 mb-6">
        <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
          <svg class="w-5 h-5 mr-2 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Troubleshooting Steps
        </h3>
        <div class="space-y-3">
          <div v-for="(step, index) in troubleshootingSteps" :key="index" class="flex items-start space-x-3">
            <div class="flex-shrink-0 w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center text-blue-300 text-sm font-medium">
              {{ index + 1 }}
            </div>
            <p class="text-slate-300 text-sm">{{ step }}</p>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex space-x-4">
        <button
          @click="retryServices"
          :disabled="isRetrying"
          class="flex-1 py-3 px-6 bg-gradient-to-r from-blue-600 to-cyan-600 text-white font-medium rounded-lg hover:from-blue-700 hover:to-cyan-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center"
        >
          <svg v-if="isRetrying" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <svg v-else class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          {{ isRetrying ? 'Checking Services...' : 'Retry Connection' }}
        </button>
        
        <button
          @click="exitApplication"
          class="py-3 px-6 bg-slate-600 text-white font-medium rounded-lg hover:bg-slate-700 transition-all"
        >
          Exit Application
        </button>
      </div>

      <!-- Last Check Time -->
      <div class="text-center mt-6">
        <p class="text-slate-400 text-sm">
          Last checked: {{ serviceStatus.lastChecked ? new Date(serviceStatus.lastChecked).toLocaleTimeString() : 'Never' }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useSystemStore } from '@/stores/system'

const router = useRouter()
const systemStore = useSystemStore()

// Props
const props = defineProps<{
  error?: any
}>()

// State
const lastCheckTime = ref<Date | null>(null)
const isRetrying = ref(false)

// Computed
const errorTitle = computed(() => 'Service Unavailable')

const errorMessage = computed(() => {
  return props.error?.message || 'One or more required services are not available. Please check the service status and try again.'
})

const troubleshootingSteps = computed(() => [
  'Check that the Ghost Tweaker backend service is running',
  'Verify network connectivity',
  'Try restarting the application',
  'Run the application as Administrator'
])

const serviceStatus = computed(() => ({
  backend: systemStore.serviceStatus?.backendServiceRunning ?? false,
  auth: systemStore.serviceStatus?.authServerConnected ?? false,
  lastChecked: lastCheckTime.value
}))

// Methods
const retryServices = async () => {
  lastCheckTime.value = new Date()
  isRetrying.value = true

  try {
    await systemStore.refreshSystemState()

    if (systemStore.isSystemReady) {
      // Services are now available, redirect to login
      router.push('/login')
    }
  } catch (error) {
    console.error('Error during service retry:', error)
  } finally {
    isRetrying.value = false
  }
}

const exitApplication = () => {
  if (window.electronAPI?.showMessageBox) {
    window.electronAPI.showMessageBox({
      type: 'question',
      title: 'Exit Application',
      message: 'Are you sure you want to exit Ghost Tweaker?',
      buttons: ['Exit', 'Cancel'],
      defaultId: 1
    }).then((result: any) => {
      if (result.response === 0) {
        window.close()
      }
    })
  } else {
    window.close()
  }
}

// Lifecycle
onMounted(async () => {
  // Initial service status check
  await systemStore.refreshSystemState()
  lastCheckTime.value = new Date()
})
</script>
